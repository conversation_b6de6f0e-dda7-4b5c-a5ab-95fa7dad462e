const Database = require('better-sqlite3');
const { Pool } = require('pg');
const path = require('path');
const fs = require('fs');
const configService = require('./config-service');
const dotenv = require('dotenv');
dotenv.config();

const dbMacStudio = process.env.DB_MAC_STUDIO === 'true';

// TimescaleDB Bağlantı Konfigürasyonu

// TimescaleDB Bağlantı Konfigürasyonu
const DB_CONFIG = dbMacStudio ? {
  host: "Taners-Mac-Studio.local",
  port: 5432,
  database: "postgres",
  user: "kripto_user",
  password: "TKripto1!"
} : {
  host: "localhost",
  port: 5432,
  database: "postgres",
  user: "tanersubasi",
  password: ""
};
// console.log('DB_CONFIG2:', DB_CONFIG.host, DB_CONFIG.user);

// Function to get the current date in YYYY-MM-DD format
const getCurrentDate = () => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
};

// Function to load database configuration
const loadDatabaseConfig = () => {
  try {
    const configPath = path.join(__dirname, 'config.json');
    const configData = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configData);
    return config.databaseServer || 'sqlite';
  } catch (error) {
    // Default to sqlite if config can't be loaded
    return 'sqlite';
  }
};

// Function to get the SQLite database file path
const getDBFilePath = async () => {
  try {
    // Get configID from config service
    let conf = await configService.getConfig();
    const configID = configService.getConfigID();
    let klineInterval = conf.websocket.klineInterval || '1m';

    const dbDir = path.join(__dirname, 'data');
    // Ensure the data directory exists
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
    return path.join(dbDir, `${configID}-${klineInterval}-ticks-${getCurrentDate()}.sqlite`);
  } catch (error) {
    // Fallback to original naming if config service fails
    const dbDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
    return path.join(dbDir, `${configID}-${klineInterval}-ticks-${getCurrentDate()}.sqlite`);
  }
};

/**
 * Fetches trading pair data from SQLite database.
 * @param {string} symbol - The trading pair symbol (e.g., 'btcusdt').
 * @returns {Promise<{lastRecordTime: number | null, totalRecords: number, closedKlineCount: number}>}
 */
const getTradingPairDataSQLite = async (symbol) => {
  const logger = require('./logger');
  const dbFile = await getDBFilePath();
  let db;

  try {
    db = new Database(dbFile);
    const tableName = symbol.toLowerCase();

    // Check if table exists
    const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get(tableName);
    if (!tableExists) {
      return { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
    }

    // Query for last record time (kline_end_time)
    const lastRecordQuery = db.prepare(`SELECT kline_end_time, timestamp FROM "${tableName}" ORDER BY timestamp DESC LIMIT 1`);
    const lastRecordResult = lastRecordQuery.get();
    const lastRecordTime = lastRecordResult ? lastRecordResult.kline_end_time : null;
    const lastTimestamp = lastRecordResult ? lastRecordResult.timestamp : null;

    // Query for total number of records
    const totalRecordsQuery = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}"`);
    const totalRecordsResult = totalRecordsQuery.get();
    const totalRecords = totalRecordsResult ? totalRecordsResult.count : 0;

    // Query for number of closed klines
    const closedKlineQuery = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}" WHERE kline_is_closed = true`);
    const closedKlineResult = closedKlineQuery.get();
    const closedKlineCount = closedKlineResult ? closedKlineResult.count : 0;

    return { lastRecordTime, totalRecords, closedKlineCount, lastTimestamp };

  } catch (err) {
    logger.error(`Error fetching SQLite data for symbol ${symbol}: ${err.message}`);
    return { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
  } finally {
    if (db) {
      db.close();
    }
  }
};

/**
 * Fetches trading pair data from TimescaleDB/PostgreSQL database.
 * @param {string} symbol - The trading pair symbol (e.g., 'btcusdt').
 * @returns {Promise<{lastRecordTime: number | null, totalRecords: number, closedKlineCount: number}>}
 */
const getTradingPairDataTimescale = async (symbol) => {
  const logger = require('./logger');
  let conf = await configService.getConfig();
  console.log('conf db', conf.dbType, conf.dbConn)
  const pool = new Pool(conf.dbConn || DB_CONFIG);
  let client;

  try {
    client = await pool.connect();
    const tableName = symbol.toLowerCase();

    // Check if table exists
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = $1
      );
    `;
    const tableExistsResult = await client.query(tableExistsQuery, [tableName]);

    if (!tableExistsResult.rows[0].exists) {
      return { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
    }

    // Query for last record time (kline_end_time)
    const lastRecordQuery = `SELECT kline_end_time, timestamp FROM "${tableName}" ORDER BY timestamp DESC LIMIT 1`;
    const lastRecordResult = await client.query(lastRecordQuery);
    const lastRecordTime = lastRecordResult.rows.length > 0 ? lastRecordResult.rows[0].kline_end_time : null;
    const lastTimestamp = lastRecordResult.rows.length > 0 ? lastRecordResult.rows[0].timestamp : null;

    // Query for total number of records
    const totalRecordsQuery = `SELECT COUNT(*) as count FROM "${tableName}"`;
    const totalRecordsResult = await client.query(totalRecordsQuery);
    const totalRecords = totalRecordsResult.rows.length > 0 ? parseInt(totalRecordsResult.rows[0].count) : 0;

    // Query for number of closed klines
    const closedKlineQuery = `SELECT COUNT(*) as count FROM "${tableName}" WHERE kline_is_closed = true`;
    const closedKlineResult = await client.query(closedKlineQuery);
    const closedKlineCount = closedKlineResult.rows.length > 0 ? parseInt(closedKlineResult.rows[0].count) : 0;

    return { lastRecordTime, totalRecords, closedKlineCount, lastTimestamp };

  } catch (err) {
    logger.error(`Error fetching TimescaleDB data for symbol ${symbol}: ${err.message}`);
    return { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
};

/**
 * Fetches trading pair data from the configured database.
 * @param {string} symbol - The trading pair symbol (e.g., 'btcusdt').
 * @returns {Promise<{lastRecordTime: number | null, totalRecords: number, closedKlineCount: number}>}
 */
const getTradingPairData = async (symbol) => {
  const databaseServer = loadDatabaseConfig();

  if (databaseServer.toLowerCase() === 'timescaledb' || databaseServer.toLowerCase() === 'postgresql') {
    return await getTradingPairDataTimescale(symbol);
  } else {
    return await getTradingPairDataSQLite(symbol);
  }
};

/**
 * Fetches trading pair data for multiple symbols from TimescaleDB/PostgreSQL database in a single query.
 * @param {string[]} symbols - Array of trading pair symbols.
 * @returns {Promise<Object>} - Object with symbol as key and data as value.
 */
const getMultipleTradingPairDataTimescale = async (symbols) => {
  const logger = require('./logger');
  let conf = await configService.getConfig();
  // console.log('conf db', conf.dbType, conf.dbConn);
  // console.log('getMultipleTradingPairDataTimescale DB_CONFIG3:', DB_CONFIG.host, DB_CONFIG.user);
  const pool = new Pool(conf.dbConn || DB_CONFIG);
  let client;

  try {
    client = await pool.connect();
    
    // Build UNION ALL query for all symbols
    const queries = symbols.map((symbol, index) => {
      const tableName = symbol.toLowerCase();
      return `
        SELECT 
          '${symbol}' as symbol,
          (SELECT kline_end_time FROM "${tableName}" ORDER BY timestamp DESC LIMIT 1) as last_record_time,
          (SELECT timestamp FROM "${tableName}" ORDER BY timestamp DESC LIMIT 1) as last_timestamp,
          (SELECT COUNT(*) FROM "${tableName}") as total_records,
          (SELECT COUNT(*) FROM "${tableName}" WHERE kline_is_closed = true) as closed_kline_count,
          EXISTS (SELECT FROM information_schema.tables WHERE table_name = '${tableName}') as table_exists
      `;
    });

    const unionQuery = queries.join(' UNION ALL ');
    const result = await client.query(unionQuery);
    
    // Process results into the expected format
    const pairStatuses = {};
    result.rows.forEach(row => {
      if (row.table_exists) {
        pairStatuses[row.symbol] = {
          lastRecordTime: row.last_record_time,
          totalRecords: parseInt(row.total_records) || 0,
          closedKlineCount: parseInt(row.closed_kline_count) || 0,
          lastTimestamp: row.last_timestamp
        };
      } else {
        pairStatuses[row.symbol] = {
          lastRecordTime: null,
          totalRecords: 0,
          closedKlineCount: 0
        };
      }
    });

    return pairStatuses;

  } catch (err) {
    // logger.error(`Error fetching TimescaleDB data for multiple symbols: ${err.message}`);
    // Fallback to individual queries if batch query fails
    const pairStatuses = {};
    for (const symbol of symbols) {
      pairStatuses[symbol] = await getTradingPairDataTimescale(symbol);
    }
    return pairStatuses;
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
};

/**
 * Fetches trading pair data for multiple symbols from SQLite database.
 * @param {string[]} symbols - Array of trading pair symbols.
 * @returns {Promise<Object>} - Object with symbol as key and data as value.
 */
const getMultipleTradingPairDataSQLite = async (symbols) => {
  const logger = require('./logger');
  const dbFile = await getDBFilePath();
  let db;

  try {
    db = new Database(dbFile);
    
    // Process each symbol individually as SQLite doesn't support UNION ALL in the same way
    const pairStatuses = {};
    for (const symbol of symbols) {
      const tableName = symbol.toLowerCase();

      // Check if table exists
      const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get(tableName);
      if (!tableExists) {
        pairStatuses[symbol] = { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
        continue;
      }

      // Query for last record time (kline_end_time)
      const lastRecordQuery = db.prepare(`SELECT kline_end_time, timestamp FROM "${tableName}" ORDER BY timestamp DESC LIMIT 1`);
      const lastRecordResult = lastRecordQuery.get();
      const lastRecordTime = lastRecordResult ? lastRecordResult.kline_end_time : null;
      const lastTimestamp = lastRecordResult ? lastRecordResult.timestamp : null;

      // Query for total number of records
      const totalRecordsQuery = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}"`);
      const totalRecordsResult = totalRecordsQuery.get();
      const totalRecords = totalRecordsResult ? totalRecordsResult.count : 0;

      // Query for number of closed klines
      const closedKlineQuery = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}" WHERE kline_is_closed = true`);
      const closedKlineResult = closedKlineQuery.get();
      const closedKlineCount = closedKlineResult ? closedKlineResult.count : 0;

      pairStatuses[symbol] = { lastRecordTime, totalRecords, closedKlineCount, lastTimestamp };
    }

    return pairStatuses;

  } catch (err) {
    logger.error(`Error fetching SQLite data for multiple symbols: ${err.message}`);
    // Fallback to individual queries if batch query fails
    const pairStatuses = {};
    for (const symbol of symbols) {
      pairStatuses[symbol] = await getTradingPairDataSQLite(symbol);
    }
    return pairStatuses;
  } finally {
    if (db) {
      db.close();
    }
  }
};

/**
 * Fetches trading pair data for multiple symbols from the configured database.
 * @param {string[]} symbols - Array of trading pair symbols.
 * @returns {Promise<Object>} - Object with symbol as key and data as value.
 */
const getMultipleTradingPairData = async (symbols) => {
  const databaseServer = loadDatabaseConfig();

  if (databaseServer.toLowerCase() === 'timescaledb' || databaseServer.toLowerCase() === 'postgresql') {
    return await getMultipleTradingPairDataTimescale(symbols);
  } else {
    return await getMultipleTradingPairDataSQLite(symbols);
  }
};

module.exports = {
  getTradingPairData,
  getTradingPairDataSQLite,
  getTradingPairDataTimescale,
  getMultipleTradingPairData,
  getMultipleTradingPairDataSQLite,
  getMultipleTradingPairDataTimescale,
  getDBFilePath,
  getCurrentDate,
  loadDatabaseConfig
};
