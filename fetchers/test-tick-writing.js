const databaseFactory = require('./database-factory');
const logger = require('./logger');

async function testTickWriting() {
  try {
    console.log('Testing Tick Writing Consistency...');
    
    const writer = databaseFactory.getWriter();
    console.log('Writer created:', writer.constructor.name);
    
    await writer.ensureInitialized();
    console.log('Writer initialized successfully');
    
    await writer.createTable('testbtc');
    console.log('Table created');
    
    // Create test ticks
    const testTicks = [];
    const tickCount = 25; // Test with 25 ticks
    
    for (let i = 0; i < tickCount; i++) {
      testTicks.push({
        eventType: 'kline',
        eventTime: Date.now() + i * 1000,
        symbol: 'TESTBTC',
        kline: {
          startTime: Date.now() + i * 60000,
          endTime: Date.now() + (i + 1) * 60000,
          symbol: 'TESTBTC',
          interval: '1m',
          firstTradeId: i + 1,
          lastTradeId: i + 2,
          openPrice: 50000 + i,
          closePrice: 50100 + i,
          highPrice: 50200 + i,
          lowPrice: 49900 + i,
          baseAssetVolume: 1.5 + i * 0.1,
          numberOfTrades: 10 + i,
          isClosed: true,
          quoteAssetVolume: 75000 + i * 100,
          takerBuyBaseAssetVolume: 0.8 + i * 0.05,
          takerBuyQuoteAssetVolume: 40000 + i * 50
        }
      });
    }
    
    console.log(`Created ${testTicks.length} test ticks`);
    
    // Buffer all ticks
    for (let i = 0; i < testTicks.length; i++) {
      const tick = testTicks[i];
      console.log(`Buffering tick ${i + 1}/${testTicks.length}`);
      await writer.bufferTick('testbtc', tick);
      
      // Add small delay to simulate real-world scenario
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    console.log('All ticks buffered');
    
    // Force flush all remaining data
    console.log('Flushing all data...');
    await writer.flushAll();
    console.log('Flush completed');
    
    // Close database
    await writer.closeDatabase();
    console.log('Database closed successfully');
    
    console.log(`✅ Test completed! All ${tickCount} ticks should be written to database.`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

testTickWriting();
