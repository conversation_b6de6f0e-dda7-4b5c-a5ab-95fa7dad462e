const TickCollector = require('./tick-collector');
const TickWorker = require('./tick-worker');
const logger = require('./logger');

const dotenv = require('dotenv');
dotenv.config();
// console.log('Environment variables loaded:', process.env, 'DEBUG_MODE:', process.env.DEBUG_MODE === 'true',  'DB_MAC_STUDIO:', process.env.DB_MAC_STUDIO, process.env.DB_MAC_STUDIO === 'true');
class BinanceTickCollectorSystem {
  constructor() {
    this.tickCollector = null;
    this.tickWorker = null;
    this.healthServer = null;
    this.stopped = false;
  }

  async start() {
    logger.info('Starting Binance Tick Collector System...');
    
    try {
      // Initialize and start tick collector
      this.tickCollector = new TickCollector();
      await this.tickCollector.start();

      // Initialize and start tick workers
      this.tickWorker = new TickWorker();
      this.tickWorker.start();

      logger.info('Binance Tick Collector System started successfully');

      // Log system info
      this.logSystemInfo();
    } catch (error) {
      logger.error(`Failed to start system: ${error.message}`);
    }
  }

  logSystemInfo() {
    const os = require('os');
    const cpuCount = os.cpus().length;
    const totalMemory = Math.round(os.totalmem() / (1024 * 1024 * 1024));
    
    logger.info('=== System Information ===');
    logger.info(`CPU Cores: ${cpuCount}`);
    logger.info(`Total Memory: ${totalMemory} GB`);
    logger.info(`Platform: ${os.platform()} ${os.arch()}`);
    logger.info(`Node.js Version: ${process.version}`);
    logger.info('========================');
  }

  async shutdown() {
    logger.info('Shutting down Binance Tick Collector System...');
    this.stopped = true;
    
    try {
      // Close tick collector
      if (this.tickCollector) {
        this.tickCollector.close();
      }
      
      // Close tick workers
      if (this.tickWorker) {
        await this.tickWorker.close();
      }
      
      logger.info('Binance Tick Collector System shut down successfully');
    } catch (error) {
      logger.error(`Error during shutdown: ${error.message}`);
    }
  }
}

// Create and start the system
const system = new BinanceTickCollectorSystem();

// Handle graceful shutdown
const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
signals.forEach(signal => {
  process.on(signal, async () => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    await system.shutdown();
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error(`Uncaught Exception: ${error.message}`);
  logger.error(error.stack);
  system.shutdown();
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  system.shutdown();
});

// Start the system
// system.start();


module.exports = system;
const http = require('http');
const { connection } = require('./queue');
const port = 3000;

async function getMetrics() {
  try {
    // Get queue metrics
    const queue = require('./queue').tickQueue;
    const counts = await queue.getJobCounts();
    
    // Format metrics in Prometheus format
    let metrics = '';
    
    // Queue metrics
    metrics += '# HELP tick_queue_jobs Number of jobs in tick queue\n';
    metrics += '# TYPE tick_queue_jobs gauge\n';
    metrics += `tick_queue_jobs{state="waiting"} ${counts.waiting || 0}\n`;
    metrics += `tick_queue_jobs{state="active"} ${counts.active || 0}\n`;
    metrics += `tick_queue_jobs{state="completed"} ${counts.completed || 0}\n`;
    metrics += `tick_queue_jobs{state="failed"} ${counts.failed || 0}\n`;
    metrics += `tick_queue_jobs{state="delayed"} ${counts.delayed || 0}\n`;
    
    // System metrics
    metrics += '# HELP process_uptime_seconds Process uptime in seconds\n';
    metrics += '# TYPE process_uptime_seconds gauge\n';
    metrics += `process_uptime_seconds ${process.uptime()}\n`;
    
    // Memory metrics
    const memoryUsage = process.memoryUsage();
    metrics += '# HELP process_memory_bytes Process memory usage in bytes\n';
    metrics += '# TYPE process_memory_bytes gauge\n';
    metrics += `process_memory_bytes{type="heap_used"} ${memoryUsage.heapUsed}\n`;
    metrics += `process_memory_bytes{type="heap_total"} ${memoryUsage.heapTotal}\n`;
    metrics += `process_memory_bytes{type="rss"} ${memoryUsage.rss}\n`;
    metrics += `process_memory_bytes{type="external"} ${memoryUsage.external}\n`;
    
    return metrics;
  } catch (error) {
    logger.error(`Error getting metrics: ${error.message}`);
    throw error;
  }
}

const requestHandler = async (req, res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(204);
    res.end();
    return;
  }

  if (req.method === 'POST' && req.url === '/server/start') {
    try {
      await system.start();
      res.writeHead(200, { 'Content-Type': 'text/plain' });
      res.end('System started\n');
    } catch (error) {
      res.writeHead(500, { 'Content-Type': 'text/plain' });
      res.end(`Failed to start system: ${error.message}\n`);
    }
  } else if (req.method === 'POST' && req.url === '/server/stop') {
    system.shutdown();
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('System stopped\n');
  }  else if (req.method === 'POST' && req.url === '/db/reset') {

    logger.info('Database reset ');
    // drop tables from the selected db source...
    const databaseFactory = require('./database-factory');
    const writer = databaseFactory.getWriter();
    await writer.resetDatabase();
    logger.info('Database reset successfully');

    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('Database reset successfully\n');
  } else if (req.method === 'GET' && req.url === '/server/terminate') {
    console.log('Terminating application');
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('System terminated\n');
    process.exit(0);
  } else if (req.method === 'GET' && req.url === '/server/ping') {
    const now = new Date();
    const response = {
      time: now.toISOString(),
      status: 'working'
    };
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  } else if (req.method === 'GET' && req.url === '/pairs') {
    try {
      const configService = require('./config-service');
      const dbQueryUtils = require('./db-query-utils');

      await configService.getConfig();
      const symbolsConfig = configService.getSymbolsConfig();

      const pairs = [];
      for (const connectionConfig of symbolsConfig.connections) {
        pairs.push(...connectionConfig.pairs);
      }
      
      // Use the optimized function to get data for all pairs in a single query
      const pairStatuses = await dbQueryUtils.getMultipleTradingPairData(pairs);
      
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(pairStatuses));
    } catch (error) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: `Failed to get pairs: ${error.message}` }));
    }
  } else if (req.method === 'GET' && req.url === '/health') {
    const response = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  } else if (req.method === 'GET' && req.url === '/status') {
    const queue = require('./queue').tickQueue;
    const os = require('os');
    const cpuCount = os.cpus().length;
    const totalMemory = Math.round(os.totalmem() / (1024 * 1024 * 1024));
    const status = {
      timestamp: new Date().toISOString(),
      system: {
        cpuCores: cpuCount,
        totalMemoryGB: totalMemory,
        platform: os.platform() + ' ' + os.arch(),
        nodeVersion: process.version,
        queueLength: await queue.count()
      },
      tickerStatus: {
        tickCollector: system.tickCollector ? system.tickCollector.getStatus() : null,
        tickWorker: system.tickWorker ? system.tickWorker.getStatus() : null
      }
    };
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(status));
  } else if (req.method === 'GET' && req.url === '/metrics') {
    try {
      const metrics = await getMetrics();
      res.writeHead(200, { 'Content-Type': 'text/plain' });
      res.end(metrics);
    } catch (error) {
      logger.error(`Error getting metrics: ${error.message}`);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        error: 'Failed to get metrics',
        message: error.message
      }));
    }
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found\n');
  }
};

const server = http.createServer(requestHandler);

server.listen(port, (err) => {
  if (err) {
    return console.log('something bad happened', err)
  }

  console.log(`server is listening on ${port}`)
})
