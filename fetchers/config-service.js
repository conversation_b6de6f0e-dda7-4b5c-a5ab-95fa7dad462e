const http = require('http');
const https = require('https');
const logger = require('./logger');

class ConfigService {
  constructor() {
    this.config = null;
    this.configEndpoint = 'http://localhost:3001/api/server/config';
    this.lastFetchTime = null;
    this.fetchInterval = 5 * 60 * 1000; // 5 minutes
  }

  async fetchConfig() {
    try {
      // logger.info('Fetching configuration from endpoint...');
      
      const response = await this.makeHttpRequest(this.configEndpoint);
      const data = JSON.parse(response);
      
      if (!data.config) {
        throw new Error('Invalid response format: missing config property');
      }
      
      // Transform the API response to match our internal structure
      this.config = {
        configID: data.config.configID,
        websocket: {
          pairsPerConnection: data.config.websocket.pairsPerConnection || 25,
          klineInterval: data.config.intervals && data.config.intervals.length > 0 ? data.config.intervals[0] : '1m',
          healthCheckInterval: data.config.websocket.healthCheckInterval || 10000,
          reconnectInterval: data.config.websocket.reconnectInterval || 5000,
          maxReconnectAttempts: data.config.websocket.maxReconnectAttempts || 10
        },
        symbols: {
          connections: [
            {
              id: 1,
              pairs: data.config.pairs || []
            }
          ]
        },
        dbType: data.config.dbType || 'local-pgtimescale',
        dbConn: data.config.dbType === 'remote-pgtimescale' ? {
          host: "Taners-Mac-Studio.local",
          port: 5432,
          database: "postgres",
          user: "kripto_user",
          password: "TKripto1!"
        } : {
          host: "localhost",
          port: 5432,
          database: "postgres",
          user: "tanersubasi",
          password: ""
        }
      };
      
      this.lastFetchTime = Date.now();
      logger.info(`Configuration fetched successfully. ConfigID: ${this.config.configID}, Pairs: ${this.config.symbols.connections[0].pairs.length}`);
      
      return this.config;
    } catch (error) {
      logger.error(`Failed to fetch configuration: ${error.message}`);
      throw error;
    }
  }

  makeHttpRequest(url) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const req = client.get(url, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        });
      });
      
      req.on('error', (error) => {
        reject(error);
      });
      
      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  async getConfig() {
    // If we don't have config or it's been too long since last fetch, fetch new config
    if (!this.config || (Date.now() - this.lastFetchTime) > this.fetchInterval) {
      await this.fetchConfig();
    }
    
    return this.config;
  }

  getWebsocketConfig() {
    if (!this.config) {
      throw new Error('Configuration not loaded. Call getConfig() first.');
    }
    return this.config.websocket;
  }

  getSymbolsConfig() {
    if (!this.config) {
      throw new Error('Configuration not loaded. Call getConfig() first.');
    }
    return this.config.symbols;
  }

  getConfigID() {
    if (!this.config) {
      throw new Error('Configuration not loaded. Call getConfig() first.');
    }
    return this.config.configID;
  }

  // Method to force refresh configuration
  async refreshConfig() {
    this.lastFetchTime = null;
    return await this.getConfig();
  }
}

// Export singleton instance
module.exports = new ConfigService();
